<script setup lang="ts">
const categories = [
  {
    id: 'waterfront',
    title: 'Waterfront Living',
    description:
      'Wake up to breathtaking water views and enjoy a serene lifestyle by the water.',
    image:
      'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?q=80&w=1470&auto=format&fit=crop',
  },
  {
    id: 'urban',
    title: 'Urban Sophistication',
    description:
      'Embrace city living with vibrant culture, dining, and entertainment at your doorstep.',
    image:
      'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?q=80&w=1470&auto=format&fit=crop',
  },
  {
    id: 'countryside',
    title: 'Country Estates',
    description:
      'Enjoy peace and privacy with sprawling grounds and breathtaking natural surroundings.',
    image:
      'https://images.unsplash.com/photo-1681653211492-1d785bf1a227?q=80&w=3281&auto=format&fit=crop',
  },
];
</script>
<template>
  <section class="py-16 bg-estate-ivory">
    <div class="container px-4 sm:px-6 mx-auto">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-title mb-4">Find Your Lifestyle</h2>
        <p class="text-estate-taupe max-w-2xl mx-auto">
          We believe in matching homes to lifestyles, not just needs. Explore our curated categories to find the perfect setting for your life story.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div
          v-for="category in categories"
          :key="category.id"
          class="group cursor-pointer"
        >
          <NuxtLink :to="`/properties?category=${category.id}`">
            <div class="relative h-80 overflow-hidden rounded-lg">
              <img
                :src="category.image"
                :alt="category.title"
                class="w-full h-full object-cover transition-all duration-500 group-hover:scale-105"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex flex-col justify-end p-6 text-white">
                <h3 class="font-serif text-2xl mb-2">{{ category.title }}</h3>
                <p class="text-gray-200">{{ category.description }}</p>
              </div>
            </div>
          </NuxtLink>
        </div>
      </div>
    </div>
  </section>
</template>


