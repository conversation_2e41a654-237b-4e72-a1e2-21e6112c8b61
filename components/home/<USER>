<script lang="ts" setup>
import {Button} from '@/components/ui/button';
</script>
<template>
    <main class="w-screen h-screen overflow-hidden relative flex items-center">
        <div
            class="absolute inset-0 z-0 bg-cover bg-center bg-[url('https://images.unsplash.com/photo-1600585154340-be6161a56a0c?q=80&w=1470&auto=format&fit=crop')]">
            <div class="absolute inset-0 bg-black opacity-40"></div>
        </div>
        <div class="container relative z-10 px-8 md:px-25 text-white">
            <div class="max-w-2xl">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-title font-semibold mb-6 animate-fade-in">
                    Find Your Perfect Lifestyle
                </h1>
                <p class="md:text-xl mb-8 animate-fade-in">
                    Discover exceptional properties that complement your unique way of life. Experience homes that
                    inspire, nurture, and elevate.
                </p>
                <div>
                    <Button class="bg-transparent" variant="outline" @click="$router.push('/properties')">
                        Explore Properties
                    </Button>
                </div>
            </div>
            

        </div>
    </main>
</template>