<script setup lang="ts">



</script>
<template>
    <section class="py-12 bg-[#f8f5f1] sm:py-16 lg:py-20 xl:py-24">
        <div class="px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
            <div class="grid grid-cols-1 lg:grid-cols-8 lg:gap-x-16 xl:gap-x-24 gap-y-12">
                <div class="lg:col-span-5 xl:pr-24">
                    <div class="max-w-lg lg:max-w-none">
                        <p class="text-base font-semibold text-gray-500">Curated Selection</p>
                        <h2
                            class="mt-6 text-3xl font-title font-semibold tracking-tight text-gray-900 lg:mt-8 sm:text-4xl lg:text-5xl">
                            Experience the Difference!</h2>
                        <p
                            class="mt-4 text-base font-normal leading-7 text-gray-600 lg:text-lg lg:pr-24 lg:mt-6 lg:leading-8">
                            We don't just list properties—we curate experiences. Our platform is designed to help you
                            envision the lifestyle each home offers, not just its specifications.</p>
                    </div>

                    <div
                        class="grid grid-cols-1 pt-8 mt-8 border-t border-gray-200 sm:grid-cols-2 md:mt-16 xl:mt-24 gap-x-16 gap-y-8">
                        <div class="flex flex-col gap-2">
                            <p>Builder Partnerships</p>
                            <h3 class="text-lg font-medium text-gray-900">We work directly with premium builders to
                                offer you exclusive access to their finest properties.</h3>
                        </div>

                        <div class="flex flex-col gap-2">
                            <p>Personalized Support</p>
                            <h3 class="text-lg font-medium text-gray-900">Our team provides tailored guidance throughout
                                your property journey.</h3>
                        </div>

                    </div>

                    <div class="mt-12">
                        <a href="#" title=""
                            class="inline-flex items-center text-sm font-semibold text-blue-600 transition-all duration-200 group hover:text-blue-800 hover:underline">
                            Explore Properties
                            <svg class="w-5 h-5 ml-1 group-hover:translate-x-0.5 group-hover:-translate-y-0.5 transition-all duration-200"
                                viewBox="0 0 24 24" stroke="currentColor" stroke-width="2.5" fill="none"
                                stroke-linecap="round" stroke-linejoin="round">
                                <line x1="7" y1="17" x2="17" y2="7"></line>
                                <polyline points="7 7 17 7 17 17"></polyline>
                            </svg>
                        </a>
                    </div>
                </div>

                <div class="lg:col-span-3">
                    <NuxtImg class="w-full xl:h-full xl:object-cover rounded-3xl "
                        src="/imgs/full-shot-happy-family-celebrating-home.jpg"
                        alt="" />
                </div>
            </div>
        </div>
    </section>

</template>