<script setup>

// State for mobile menu
const expanded = ref(false);

// State for scroll detection
const isScrolled = ref(false);
const scrollThreshold = 50; // Pixels scrolled before changing navbar background

// Function to handle scroll events
const handleScroll = () => {
  isScrolled.value = window.scrollY > scrollThreshold;
};

// Add and remove scroll event listener
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  // Check initial scroll position
  handleScroll();
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>
<template>
   <header
      :class="[
        'py-4 fixed z-50 w-full sm:py-6 transition-all duration-300',
        isScrolled ? 'bg-black shadow-md opacity-90' : 'bg-transparent'
      ]"
    >
    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
            <div class="shrink-0">
                <NuxtLink to="" class="flex">
                    <img class="w-auto h-10" src="/imgs/logo.png" alt="" />
                </NuxtLink>
            </div>

            <div class="flex md:hidden">
                <button
                    type="button"
                    :class="isScrolled ? 'text-gray-800' : 'text-white'"
                    @click="expanded = !expanded"
                    :aria-expanded="expanded"
                >
                    <span x-show="!expanded" aria-hidden="true">
                        <svg class="w-7 h-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </span>

                    <span x-show="expanded" aria-hidden="true">
                        <svg class="w-7 h-7" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </span>
                </button>
            </div>

            <nav class="hidden ml-auto mr-10 space-x-10 md:flex md:items-center md:justify-end lg:space-x-12">
                <NuxtLink
                    to="/"
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-white hover:text-purple-700' : 'text-white hover:text-white'
                    ]"
                >
                    Home
                </NuxtLink>

                <NuxtLink
                    to="/properties"
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-white hover:text-purple-700' : 'text-white hover:text-white'
                    ]"
                >
                    Properties
                </NuxtLink>

                <NuxtLink
                    to=""
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-white hover:text-purple-700' : 'text-white hover:text-white'
                    ]"
                >
                    Support
                </NuxtLink>

            </nav>

            <div class="relative hidden md:items-center md:justify-center md:inline-flex group">
                <div
                    class="absolute transition-all duration-200 rounded-full -inset-px bg-gradient-to-r from-cyan-500 to-purple-500 group-hover:shadow-lg group-hover:shadow-cyan-500/50"
                ></div>
                <NuxtLink
                    to="/properties"
                    :class="[
                        'relative inline-flex items-center justify-center px-6 py-2 text-base font-normal border border-transparent rounded-full transition-all duration-300',
                        isScrolled
                            ? 'text-white bg-purple-600 hover:bg-purple-700'
                            : 'text-white bg-black'
                    ]"
                    role="button"
                >
                    Explore Properties
                </NuxtLink>
            </div>
        </div>

        <nav v-if="expanded" :class="isScrolled ? 'bg-white' : 'bg-gray-900'">
            <div class="flex flex-col pt-8 pb-4 space-y-6">
                <NuxtLink
                    to="/"
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-gray-800 hover:text-purple-700' : 'text-gray-400 hover:text-white'
                    ]"
                >
                    Home
                </NuxtLink>

                <NuxtLink
                    to="/properties"
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-gray-800 hover:text-purple-700' : 'text-gray-400 hover:text-white'
                    ]"
                >
                    Properties
                </NuxtLink>

                <NuxtLink
                    to=""
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-gray-800 hover:text-purple-700' : 'text-gray-400 hover:text-white'
                    ]"
                >
                    Support
                </NuxtLink>

                <div class="relative inline-flex items-center justify-center group">
                    <div class="absolute transition-all duration-200 rounded-full -inset-px bg-gradient-to-r from-cyan-500 to-purple-500 group-hover:shadow-lg group-hover:shadow-cyan-500/50"></div>
                    <NuxtLink
                        to="/properties"
                        :class="[
                            'relative inline-flex items-center justify-center w-full px-6 py-2 text-base font-normal border border-transparent rounded-full',
                            isScrolled ? 'text-white bg-purple-600' : 'text-white bg-black'
                        ]"
                        role="button"
                    >
                        Explore Properties
                    </NuxtLink>
                </div>
            </div>
        </nav>
    </div>
</header>

</template>

