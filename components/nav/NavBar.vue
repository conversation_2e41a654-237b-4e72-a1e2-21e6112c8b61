<script setup>

// State for mobile menu
const expanded = ref(false);

// State for scroll detection
const isScrolled = ref(false);
const scrollThreshold = 50; // Pixels scrolled before changing navbar background

// Function to handle scroll events
const handleScroll = () => {
  isScrolled.value = window.scrollY > scrollThreshold;
};

// Add and remove scroll event listener
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
  // Check initial scroll position
  handleScroll();
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>
<template>
   <header
      :class="[
        'py-4 fixed z-50 w-full sm:py-6 transition-all duration-300',
        isScrolled ? 'bg-black shadow-md opacity-90' : 'bg-transparent'
      ]"
    >
    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
            <div class="shrink-0">
                <NuxtLink to="" class="flex">
                    <img class="w-auto h-10" src="/imgs/logo.png" alt="" />
                </NuxtLink>
            </div>

            <div class="flex md:hidden">
                <button
                    type="button"
                    :class="[
                        'p-2 rounded-md transition-colors duration-200',
                        isScrolled ? 'text-white hover:bg-gray-100 hover:text-gray-800' : 'text-white hover:bg-white/10'
                    ]"
                    @click="expanded = !expanded"
                    :aria-expanded="expanded"
                    aria-label="Toggle navigation menu"
                >
                    <!-- Hamburger Icon -->
                    <svg
                        v-show="!expanded"
                        class="w-6 h-6"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        aria-hidden="true"
                    >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>

                    <!-- Close Icon -->
                    <svg
                        v-show="expanded"
                        class="w-6 h-6"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        aria-hidden="true"
                    >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <nav class="hidden ml-auto mr-10 space-x-10 md:flex md:items-center md:justify-end lg:space-x-12">
                <NuxtLink
                    to="/"
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-white hover:text-purple-700' : 'text-white hover:text-white'
                    ]"
                >
                    Home
                </NuxtLink>

                <NuxtLink
                    to="/properties"
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-white hover:text-purple-700' : 'text-white hover:text-white'
                    ]"
                >
                    Properties
                </NuxtLink>

                <NuxtLink
                    to=""
                    :class="[
                        'text-base font-normal transition-all duration-200',
                        isScrolled ? 'text-white hover:text-purple-700' : 'text-white hover:text-white'
                    ]"
                >
                    Support
                </NuxtLink>

            </nav>

            <div class="relative hidden md:items-center md:justify-center md:inline-flex group">
                <div
                    class="absolute transition-all duration-200 rounded-full -inset-px bg-gradient-to-r from-cyan-500 to-purple-500 group-hover:shadow-lg group-hover:shadow-cyan-500/50"
                ></div>
                <NuxtLink
                    to="/properties"
                    :class="[
                        'relative inline-flex items-center justify-center px-6 py-2 text-base font-normal border border-transparent rounded-full transition-all duration-300',
                        isScrolled
                            ? 'text-white bg-purple-600 hover:bg-purple-700'
                            : 'text-white bg-black'
                    ]"
                    role="button"
                >
                    Explore Properties
                </NuxtLink>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <nav
            v-if="expanded"
            :class="[
                'absolute top-full left-0 right-0 shadow-lg border-t transition-all duration-300 ease-in-out',
                isScrolled ? 'bg-white border-gray-200' : 'bg-gray-900 border-gray-700'
            ]"
        >
            <div class="px-4 py-6 space-y-4">
                <!-- Navigation Links -->
                <div class="space-y-3">
                    <NuxtLink
                        to="/"
                        @click="expanded = false"
                        :class="[
                            'block px-4 py-3 text-lg font-medium rounded-lg transition-all duration-200',
                            isScrolled
                                ? 'text-gray-800 hover:text-purple-700 hover:bg-purple-50'
                                : 'text-gray-300 hover:text-white hover:bg-gray-800'
                        ]"
                    >
                        Home
                    </NuxtLink>

                    <NuxtLink
                        to="/properties"
                        @click="expanded = false"
                        :class="[
                            'block px-4 py-3 text-lg font-medium rounded-lg transition-all duration-200',
                            isScrolled
                                ? 'text-gray-800 hover:text-purple-700 hover:bg-purple-50'
                                : 'text-gray-300 hover:text-white hover:bg-gray-800'
                        ]"
                    >
                        Properties
                    </NuxtLink>

                    <NuxtLink
                        to=""
                        @click="expanded = false"
                        :class="[
                            'block px-4 py-3 text-lg font-medium rounded-lg transition-all duration-200',
                            isScrolled
                                ? 'text-gray-800 hover:text-purple-700 hover:bg-purple-50'
                                : 'text-gray-300 hover:text-white hover:bg-gray-800'
                        ]"
                    >
                        Support
                    </NuxtLink>
                </div>

                <!-- CTA Button -->
                <div class="pt-4 border-t" :class="isScrolled ? 'border-gray-200' : 'border-gray-700'">
                    <div class="relative inline-flex items-center justify-center w-full group">
                        <div class="absolute transition-all duration-200 rounded-full -inset-px bg-gradient-to-r from-cyan-500 to-purple-500 group-hover:shadow-lg group-hover:shadow-cyan-500/50"></div>
                        <NuxtLink
                            to="/properties"
                            @click="expanded = false"
                            :class="[
                                'relative inline-flex items-center justify-center w-full px-6 py-3 text-base font-semibold border border-transparent rounded-full transition-all duration-300',
                                isScrolled
                                    ? 'text-white bg-purple-600 hover:bg-purple-700'
                                    : 'text-white bg-black hover:bg-gray-800'
                            ]"
                            role="button"
                        >
                            Explore Properties
                        </NuxtLink>
                    </div>
                </div>
            </div>
        </nav>
    </div>
</header>

</template>

