<template>
  <div>
    <div ref="mapContainer" class="map-container rounded-lg overflow-hidden border"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

const props = defineProps({
  longitude: {
    type: Number,
    default: -73.9857,  // Default to New York City
  },
  latitude: {
    type: Number,
    default: 40.7484,
  },
  zoom: {
    type: Number,
    default: 14,
  },
  mapStyle: {
    type: String,
    default: 'mapbox://styles/mapbox/light-v11',
  },
  markerColor: {
    type: String,
    default: '#6366f1', // Indigo color
  }
});

const mapContainer = ref(null);
let map = null;
let marker = null;

// Initialize the map
const initializeMap = () => {
  // Set your Mapbox access token here
  // For production, you should use environment variables
  mapboxgl.accessToken = 'pk.eyJ1IjoiZXhhbXBsZXVzZXIiLCJhIjoiY2xvYWRmcm9tdGVzdCJ9.example-signature';

  // Create the map instance
  map = new mapboxgl.Map({
    container: mapContainer.value,
    style: props.mapStyle,
    center: [props.longitude, props.latitude],
    zoom: props.zoom,
    interactive: true,
  });

  // Add navigation controls
  map.addControl(new mapboxgl.NavigationControl(), 'top-right');

  // Add a marker at the specified location
  marker = new mapboxgl.Marker({
    color: props.markerColor,
    draggable: false,
  })
    .setLngLat([props.longitude, props.latitude])
    .addTo(map);

  // Add a popup to the marker
  new mapboxgl.Popup({
    offset: 25,
    closeButton: false,
    className: 'custom-popup'
  })
    .setLngLat([props.longitude, props.latitude])
    .setHTML('<div class="font-medium">Property Location</div>')
    .addTo(map);

  // Add event listeners
  map.on('load', () => {
    // Add custom layers or data sources here if needed
    
    // Add a pulsing dot effect
    map.addSource('point', {
      'type': 'geojson',
      'data': {
        'type': 'FeatureCollection',
        'features': [
          {
            'type': 'Feature',
            'geometry': {
              'type': 'Point',
              'coordinates': [props.longitude, props.latitude]
            },
            'properties': {}
          }
        ]
      }
    });
    
    map.addLayer({
      'id': 'point',
      'source': 'point',
      'type': 'circle',
      'paint': {
        'circle-radius': 15,
        'circle-color': props.markerColor,
        'circle-opacity': 0.3,
        'circle-stroke-width': 2,
        'circle-stroke-color': props.markerColor
      }
    });
  });
};

// Watch for changes in props
watch(() => [props.longitude, props.latitude], ([newLng, newLat]) => {
  if (map && marker) {
    marker.setLngLat([newLng, newLat]);
    map.flyTo({ center: [newLng, newLat] });
    
    // Update the pulsing dot
    if (map.getSource('point')) {
      map.getSource('point').setData({
        'type': 'FeatureCollection',
        'features': [
          {
            'type': 'Feature',
            'geometry': {
              'type': 'Point',
              'coordinates': [newLng, newLat]
            },
            'properties': {}
          }
        ]
      });
    }
  }
});

onMounted(() => {
  initializeMap();
});

onUnmounted(() => {
  if (map) map.remove();
});
</script>

<style scoped>
.map-container {
  width: 100%;
  height: 400px;
}

:deep(.mapboxgl-popup-content) {
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.custom-popup) {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
</style>
