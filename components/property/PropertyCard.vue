<script setup>
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { BedDouble, Bath, RulerDimensionLine, Clock, Eye, Heart, Share2, BadgeCheck } from 'lucide-vue-next'
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
    property: {
        type: Object,
        required: true,
        // Expected property structure:
        default: () => ({
            id: 0,
            imageUrl: '',
            title: '',
            location: '',
            price: {
                furnished: '',
                unfurnished: ''
            },
            isFurnished: false,
            isRental: false,
            beds: 0,
            baths: 0,
            area: 0,
            type: '',
            featured: false,
            isTopPick: false,
            listedTime: '',
            agent: '',
            agentImage: '',
            views: 0
        })
    }
});

// Navigate to property details page
const navigateToPropertyDetails = () => {
    router.push(`/properties/${props.property.id}`);
};

// Handle action button clicks without triggering navigation
const handleActionClick = (event) => {
    event.stopPropagation();
};
</script>
<template>
    <div
        class="relative border rounded-lg overflow-hidden transition duration-300 hover:shadow-lg hover:border-purple-200 group cursor-pointer"
        @click="navigateToPropertyDetails"
    >
        <!-- Card Image with Overlay Elements -->
        <div class="relative h-48">
            <!-- Property Image -->
            <img :src="property.imageUrl" :alt="property.title"
                class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-[1.02]" />

            <!-- Featured Badge -->
            <div v-if="property.featured"
                class="absolute top-4 right-4 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded">
                FEATURED
            </div>

            <!-- Top Pick Badge -->
            <div v-if="property.isTopPick"
                class="absolute top-4 left-4 bg-gray-900 text-white text-xs font-bold px-2 py-1 rounded">
                TOP PICK
            </div>

            <!-- Price Tag -->
             <div class="absolute bottom-4 left-4 flex space-x-1">
                <div
                class=" bg-white bg-opacity-90 py-1 px-2 rounded-md text-gray-800 font-semibold text-sm">
                <div>
                    {{ property.isFurnished ? property.price.furnished : property.price.unfurnished }}
                    <span v-if="property.isRental" class="font-normal text-xs">/mo</span>
                </div>
            </div>
            <div
                class=" bg-purple-100 bg-opacity-90 py-1 px-2 rounded-md text-purple-800 text-sm">
                <div>
                    {{ property.isFurnished ? 'Furnished' : 'Unfurnished' }}
                </div>
            </div>
             </div>


            <!-- Action Buttons -->
            <div class="absolute bottom-4 right-4 flex space-x-1">
                <Button
                    class="bg-white rounded-full p-2 shadow hover:bg-gray-100 h-8 w-8 text-gray-600"
                    @click="handleActionClick"
                    title="Quick view"
                >
                    <Eye class="h-5 w-5" />
                </Button>
                <Button
                    class="bg-white rounded-full p-2 shadow hover:bg-gray-100 h-8 w-8 text-gray-600"
                    @click="handleActionClick"
                    title="Save to favorites"
                >
                    <Heart class="h-4 w-4" />
                </Button>
                <Button
                    class="bg-white rounded-full p-2 shadow hover:bg-gray-100 h-8 w-8 text-gray-600"
                    @click="handleActionClick"
                    title="Share property"
                >
                    <Share2 class="h-4 w-4" />
                </Button>
            </div>
        </div>

        <!-- Property Title -->
        <div class="p-4">
            <div class="flex items-center justify-between">
                <h3 class="font-semibold text-gray-800 mb-1 group-hover:text-purple-700 transition-colors duration-200">
                    {{ property.title }}
                </h3>
                <span class="text-xs text-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    View details →
                </span>
            </div>

            <!-- Property Location -->
            <p class="text-sm text-gray-600 mb-3">{{ property.location }}</p>

            <!-- Property Features -->
            <div class="flex items-center justify-between border-t border-gray-100 pt-3">
                <!-- Beds -->
                <div class="flex items-center">
                    <BedDouble class="h-4 w-4 text-gray-500 mr-1" />
                    <span class="text-sm text-gray-600">{{ property.beds }}</span>
                </div>

                <!-- Baths -->
                <div class="flex items-center">
                    <Bath class="h-4 w-4 text-gray-500 mr-1" />
                    <span class="text-sm text-gray-600">{{ property.baths }}</span>
                </div>

                <!-- Area -->
                <div class="flex items-center">
                    <RulerDimensionLine class="h-4 w-4 text-gray-500 mr-1" />
                    <span class="text-sm text-gray-600">{{ property.area }} m²</span>
                </div>

                <!-- Property Type -->
                <div class="px-2 py-0.5 bg-gray-100 rounded-sm">
                    <span class="text-xs text-gray-700 uppercase">{{ property.type }}</span>
                </div>
            </div>

            <!-- Listing Info -->
            <div class="flex items-center justify-between mt-4 text-xs text-gray-500">
                <div class="flex items-center">
                    <Clock class="h-3 w-3 text-gray-500 mr-1" />
                    {{ property.listedTime }} ago
                </div>

                <div class="flex items-center">
                    <Eye class="h-4 w-4 text-gray-500 mr-1" />
                    {{ property.views }} views
                </div>

                <!-- Agent Info -->
                <div class="flex items-center gap-2">
                    <div class="relative">
                        <Avatar class="border">
                            <AvatarImage :src="property.agentImage" :alt="`Agent Image: ${property.agent}`" />
                            <AvatarFallback>CN</AvatarFallback>
                        </Avatar>
                        <div class="absolute -bottom-1 -right-1 bg-green-400 rounded-full p-1">
                            <BadgeCheck class="size-3 text-white" />
                        </div>
                    </div>

                    {{ property.agent }}
                </div>
            </div>
        </div>
    </div>
</template>
