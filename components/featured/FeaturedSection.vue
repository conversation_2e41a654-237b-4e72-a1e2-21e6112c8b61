<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { PropertyCard } from '@/components/property';
import { usePropertiesStore } from '@/stores/properties';

// Get the properties store
const propertiesStore = usePropertiesStore();

// Use async data to fetch featured properties
const { data: featuredProperties, error } = await useAsyncData(
  'featured-properties',
  async () => {
    await propertiesStore.fetchProperties();
    return propertiesStore.limitedFeaturedProperties(3);
  },
  {
    // Cache the data for 5 minutes
    server: true,
    lazy: false,
    default: () => [],
  }
);

// Error handling
const errorMessage = ref('');
if (error.value) {
  errorMessage.value = 'Unable to load featured properties. Please try again later.';
  console.error('Error fetching featured properties:', error.value);
}
</script>

<template>
  <section class="py-16 w-full text-center">
    <div class="container px-6 mx-auto">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-title mb-4">Featured Properties</h2>
        <p class="text-estate-taupe max-w-2xl mx-auto">
          Discover our handpicked selection of exceptional properties, each offering a unique lifestyle
          experience.
        </p>
      </div>

      <!-- Error message -->
      <div v-if="errorMessage" class="text-red-500 mb-8">
        {{ errorMessage }}
      </div>

      <!-- Use Suspense for async data loading -->
      <Suspense>
        <template #default>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <PropertyCard
              v-for="property in featuredProperties"
              :key="property.id"
              :property="property"
            />
          </div>
        </template>
        <template #fallback>
          <div class="flex justify-center items-center py-12">
            <div class="animate-pulse flex flex-col items-center">
              <div class="h-8 w-8 bg-purple-200 rounded-full mb-4"></div>
              <p class="text-gray-500">Loading featured properties...</p>
            </div>
          </div>
        </template>
      </Suspense>

      <Button class="flex justify-center mt-12 mx-auto">
        <NuxtLink to="/properties" class="primary-button">
          View All Properties
        </NuxtLink>
      </Button>
    </div>
  </section>
</template>