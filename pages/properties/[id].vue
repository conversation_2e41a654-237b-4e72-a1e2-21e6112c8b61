<script setup lang="ts">

// Components
import {
    Avatar,
    AvatarImage,
    AvatarFallback
} from '@/components/ui/avatar';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'


// Icons
import {
    Video,
    Image,
    BadgeCheck,
    Share2,
    Heart,
    Home,
    Ruler,
    Layers,
    BedDouble,
    Bath,
    ParkingCircle,
    AlertTriangle,
    Lightbulb,
    Check,
    MapPin,
    Info as InfoIcon,
    Copy,
    ClipboardCheck
} from 'lucide-vue-next';

// Map Component
import { PropertyMap } from '@/components/map';

// Data
import { watchOnce } from '@vueuse/core';
import { Card } from '@/components/ui/card';
import { Carousel, type CarouselApi, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { usePropertiesStore } from '@/stores/properties';

const route = useRoute();
const propertyId = route.params.id as string;
const activeTab = ref('essential');
const showFurnishedPrice = ref(false); // Toggle between furnished and unfurnished prices

// Get the properties store
const propertiesStore = usePropertiesStore();

// Fetch property details with useAsyncData
const { data: property, error, pending } = await useAsyncData(
    `property-${propertyId}`,
    () => propertiesStore.fetchPropertyById(propertyId),
    {
        server: true,
        lazy: false
    }
);

// Set up tags based on property data
const tags = computed(() => {
    if (!property.value) return [];

    const baseTags = ['Affordable', 'Modern'];

    if (property.value.isRental) {
        baseTags.push('For Rent');
    } else {
        baseTags.push('For Sale');
    }

    if (property.value.type) {
        baseTags.push(property.value.type);
    }

    return baseTags;
});

// Current price based on furnished/unfurnished toggle
const currentPrice = computed(() => {
    if (!property.value?.price) return 'Price not available';
    return showFurnishedPrice.value && property.value.price.furnished
        ? property.value.price.furnished
        : property.value.price.unfurnished;
});

// Property data for SEO
const propertyTitle = computed(() => property.value?.title || 'Property Details');
const propertyLocation = computed(() => property.value?.location || 'Location not available');
const propertyDescription = computed(() => {
    const baseDescription = property.value?.description || `${property.value?.title} located in ${property.value?.location}.`;
    const furnishingStatus = showFurnishedPrice.value ? 'Furnished' : 'Unfurnished';
    return `${baseDescription} Available ${furnishingStatus} for ${currentPrice.value}.`;
});
const propertyImage = computed(() => property.value?.imageUrl || 'https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=870&q=80');

// Set page title and meta
useHead({
    title: propertyTitle,
    meta: [
        { name: 'description', content: propertyDescription },
        { property: 'og:title', content: `${propertyTitle} - ${propertyLocation}` },
        { property: 'og:description', content: propertyDescription },
        { property: 'og:image', content: propertyImage },
        { property: 'og:type', content: 'product' },
        { property: 'og:price:amount', content: currentPrice },
        { property: 'og:price:currency', content: 'USD' },
        { name: 'twitter:label1', content: 'Location' },
        { name: 'twitter:data1', content: propertyLocation }
    ]
});


// Carousel functionality
const emblaMainApi = ref<CarouselApi>();
const emblaThumbnailApi = ref<CarouselApi>();
const selectedIndex = ref(0);

function onSelect() {
    if (!emblaMainApi.value || !emblaThumbnailApi.value)
        return;
    selectedIndex.value = emblaMainApi.value.selectedScrollSnap();
    emblaThumbnailApi.value.scrollTo(emblaMainApi.value.selectedScrollSnap());
}

function onThumbClick(index: number) {
    if (!emblaMainApi.value || !emblaThumbnailApi.value)
        return;
    emblaMainApi.value.scrollTo(index);
}

watchOnce(emblaMainApi, (api) => {
    if (!api)
        return;

    onSelect();
    api.on('select', onSelect);
    api.on('reInit', onSelect);
});

const openBooking = (isVirtual: boolean = false) => {
    window.open(isVirtual ? `https://cal.com/dwello/virtual-viewing?propRef=${propertyId}` : 'https://cal.com/dwello/physical-viewing', '_blank')
}

const purchase = () => {
    const url = "https://tally.so/r/mePPpE"
    window.open(url, '_blank');
}

// Property reference copy functionality
const isCopied = ref(false);
const propertyReference = computed(() => `REF-${propertyId}`);

const copyPropertyReference = () => {
    navigator.clipboard.writeText(propertyReference.value)
        .then(() => {
            isCopied.value = true;
            setTimeout(() => {
                isCopied.value = false;
            }, 2000);
        })
        .catch(err => {
            console.error('Failed to copy: ', err);
        });
}

// We'll use Tailwind's built-in animations instead of custom CSS

const contactViaWhatsApp = () => {
    const phoneNumber = '+233541393257';
    const propertyUrl = window.location.href;
    const message = `Hello, I'm interested in the property: ${propertyTitle.value} (${propertyReference.value}) at ${propertyLocation.value}. Price: ${currentPrice.value}. Property link: ${propertyUrl}`;
    
    // Encode the message for URL
    const encodedMessage = encodeURIComponent(message);
    
    // Open WhatsApp with the pre-filled message
    window.open(`https://wa.me/${phoneNumber}?text=${encodedMessage}`, '_blank');
}
</script>
<template>
    <div v-if="pending" class="bg-white min-h-screen flex items-center justify-center">
        <div class="text-center">
            <div class="animate-pulse flex flex-col items-center">
                <div class="h-16 w-16 bg-purple-200 rounded-full mb-4"></div>
                <p class="text-gray-500 text-lg">Loading property details...</p>
            </div>
        </div>
    </div>

    <div v-else-if="error" class="bg-white min-h-screen flex items-center justify-center">
        <div class="text-center max-w-md p-6 bg-white rounded-lg shadow-md">
            <div class="text-red-500 mb-4">
                <AlertTriangle class="h-12 w-12 mx-auto" />
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-2">Property Not Found</h2>
            <p class="text-gray-600 mb-6">We couldn't find the property you're looking for. It may have been removed or
                the ID is incorrect.</p>
            <Button @click="$router.push('/properties')" class="bg-purple-600 hover:bg-purple-700">
                Browse All Properties
            </Button>
        </div>
    </div>

    <div v-else class="bg-white min-h-screen">
        <header class="w-screen h-80 overflow-hidden relative flex items-center">
            <div class="absolute inset-0 z-0 bg-cover bg-top bg-[url(/imgs/house_1.jpg)]">
                <div class="absolute inset-0 bg-black opacity-70"></div>
            </div>
            <div class="container relative z-10 px-8 md:px-25 text-white mt-10">
                <div class="max-w-2xl">
                    <div class="flex items-start gap-2 flex-col">
                        <div class="relative">
                            <Avatar class="size-12 md:size-18 border-white border-1 rounded-sm shadow-md">
                                <AvatarImage :src="property?.agentImage || ''" alt="RE" />
                                <AvatarFallback>CM</AvatarFallback>
                            </Avatar>
                            <div class="absolute -bottom-1 -right-1 bg-green-400 rounded-full p-1 shadow-sm">
                                <BadgeCheck class="size-3.5 text-white" />
                            </div>
                        </div>

                        <h1 class="text-3xl md:text-5xl font-title font-semibold mb-4 animate-fade-in">
                            {{ propertyTitle }}
                        </h1>
                    </div>

                    <Breadcrumb class="text-white">
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink class="text-gray-300 text-xs" href="/">
                                    Home
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink class="text-gray-300 text-xs" href="/properties">
                                    Properties
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink class="text-gray-300 text-xs" :href="`/properties`">
                                    {{ property?.agent || '' }}
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage class="text-purple-300 text-xs">{{ propertyTitle }}</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>
            </div>
        </header>
        <!-- Notice Banner -->
        <div v-if="false" class="bg-purple-100 text-purple-700 text-xs sm:text-sm py-2.5 text-center font-medium">
            <span class="font-semibold">Early Preview</span> This property is available on Casap as an early preview
            until
            <span class="font-bold">3:30 PM</span>
        </div>

        <!-- Property gallery slide -->
        <div class="w-full sm:w-auto md:px-16 pt-2 overflow-hidden">
            <div class="relative">
                <Carousel class="relative w-full" @init-api="(val) => emblaMainApi = val">
                    <CarouselContent>
                        <CarouselItem v-for="(image, index) in property?.gallery" :key="index">
                            <div class="p-1">
                                <Card
                                     :style="{ backgroundImage: `url(${image})` }"
                                    class="border shadow-none h-[50vh] items-center justify-center p-6 bg-cover bg-center">
                      
                                </Card>
                            </div>
                        </CarouselItem>
                    </CarouselContent>
                    <CarouselPrevious />
                    <CarouselNext />
                </Carousel>
                <div class="flex justify-center mt-[-40px] absolute z-10 space-x-3 bottom-10 left-0 right-0">
                    <Button variant="default" size="sm" @click="openBooking"
                        class="rounded-full shadow-md text-xs font-medium px-4 py-2 bg-white text-gray-700 hover:bg-gray-50 border border-gray-200">
                        <Video class="mr-1.5 h-4 w-4" /> Book A Virtual Tour
                    </Button>
                </div>
            </div>

            <Carousel class="relative w-full " @init-api="(val) => emblaThumbnailApi = val">
                <CarouselContent class="flex gap-1 ml-0">
                    <CarouselItem v-for="(image, index) in property?.gallery" :key="index" class="pl-0 basis-1/4 cursor-pointer"
                        @click="onThumbClick(index)">
                        <div class="p-1" :class="index === selectedIndex ? '' : 'opacity-50'">
                            <Card
                                 :style="{ backgroundImage: `url(${image})` }"
                                class="border shadow-none h-[8vh] md:h-[20vh] items-center justify-center p-6 bg-cover bg-center">

                            </Card>
                        </div>
                    </CarouselItem>
                </CarouselContent>
            </Carousel>
        </div>

        <!-- Main Content -->
        <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">


            <div class="flex flex-col lg:flex-row lg:space-x-6 mt-6">
                <!-- Property Main Info -->
                <div class="lg:w-[calc(100%-380px-1.5rem)]">

                    <!-- Property Header -->
                    <section class="bg-white p-5 sm:p-6 rounded-lg border mb-6">
                        <div class="flex justify-between items-start mb-2">
                            <div>
                                <span
                                    class="inline-flex items-center text-[11px] font-medium bg-green-100 text-green-600 px-2.5 py-1 rounded-full">
                                    Verified Listing
                                </span>
                                <span class="text-[11px] text-gray-500 ml-2">Published on May 21, 2025 at 08:47</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <Button variant="ghost" size="icon" class="h-8 w-8 text-gray-500 hover:text-gray-700">
                                    <Share2 class="h-4 w-4" />
                                </Button>
                                <div class="bg-pink-100 p-1.5 rounded-full">
                                    <Heart class="h-4 w-4 text-pink-500 fill-pink-500" />
                                </div>
                            </div>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800 mb-1">{{ property?.title }}</h1>
                        <div class="flex items-center mb-2.5">
                            <p class="text-xl font-semibold text-purple-700">
                                {{ showFurnishedPrice && property?.price?.furnished ? property.price.furnished :
                                property?.price?.unfurnished }}
                                <span class="text-base text-gray-500 font-medium align-baseline">
                                    {{ property?.isRental ? '/month' : '' }}
                                </span>
                            </p>

                            <!-- Price toggle -->
                            <div class="ml-4 flex items-center space-x-2 bg-gray-100 rounded-full p-1">
                                <button @click="showFurnishedPrice = false"
                                    class="px-3 py-1 text-xs rounded-full transition-colors"
                                    :class="!showFurnishedPrice ? 'bg-white shadow text-purple-700 font-medium' : 'text-gray-600'">
                                    Unfurnished
                                </button>
                                <button @click="showFurnishedPrice = true"
                                    class="px-3 py-1 text-xs rounded-full transition-colors"
                                    :class="showFurnishedPrice ? 'bg-white shadow text-purple-700 font-medium' : 'text-gray-600'">
                                    Furnished
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <span>{{ property?.beds }} {{ property?.beds === 1 ? 'room' : 'rooms' }}</span>
                            <span class="mx-1.5 text-gray-400">·</span>
                            <span>{{ property?.area }}m²</span>
                            <span class="mx-1.5 text-gray-400">·</span>
                            <span>{{ property?.type }}</span>
                            <span class="mx-1.5 text-gray-400">·</span>
                            <span>{{ property?.location }}</span>
                        </div>
                    </section>

                    <!-- Tabs Navigation -->
                    <section class="mb-6 border-b border-gray-200">
                        <nav class="flex space-x-4 -mb-px">
                            <button v-for="tab in ['Essential', 'Map']" :key="tab"
                                @click="activeTab = tab.toLowerCase()" :class="[
                                    'px-3 py-2.5 text-sm font-medium leading-5 focus:outline-none',
                                    activeTab === tab.toLowerCase()
                                        ? 'border-b-2 border-purple-600 text-purple-600'
                                        : 'text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 border-transparent'
                                ]">
                                {{ tab }}
                            </button>
                        </nav>
                    </section>

                    <!-- Tab Content -->
                    <section v-if="activeTab === 'essential'" class="bg-white p-5 sm:p-6 rounded-lg border mb-6">
                        <div class="grid grid-cols-2 sm:grid-cols-3 gap-x-4 gap-y-5 text-sm">
                            <div>
                                <div class="flex items-center text-gray-400 mb-0.5">
                                    <Home class="h-4 w-4 mr-1.5" />
                                    <span class="text-[11px] font-medium">Property Type</span>
                                </div>
                                <p class="font-semibold text-gray-800 text-sm">
                                    {{ property?.type || 'Not specified' }}
                                    <span class="text-purple-600">({{ showFurnishedPrice ? 'Furnished' : 'Unfurnished'
                                        }})</span>
                                </p>
                            </div>
                            <div>
                                <div class="flex items-center text-gray-400 mb-0.5">
                                    <Ruler class="h-4 w-4 mr-1.5" />
                                    <span class="text-[11px] font-medium">Area</span>
                                </div>
                                <p class="font-semibold text-gray-800 text-sm">{{ property?.area || 0 }} m²</p>
                            </div>
                            <div>
                                <div class="flex items-center text-gray-400 mb-0.5">
                                    <Layers class="h-4 w-4 mr-1.5 transform rotate-90" />
                                    <span class="text-[11px] font-medium">Status</span>
                                </div>
                                <p class="font-semibold text-gray-800 text-sm">{{ property?.isRental ? 'For Rent' : 'For Sale' }}</p>
                            </div>
                            <div>
                                <div class="flex items-center text-gray-400 mb-0.5">
                                    <BedDouble class="h-4 w-4 mr-1.5" />
                                    <span class="text-[11px] font-medium">Bedrooms</span>
                                </div>
                                <p class="font-semibold text-gray-800 text-sm">{{ property?.beds || 0 }} {{
                                    property?.beds === 1 ? 'bedroom' : 'bedrooms' }}</p>
                            </div>
                            <div>
                                <div class="flex items-center text-gray-400 mb-0.5">
                                    <Bath class="h-4 w-4 mr-1.5" />
                                    <span class="text-[11px] font-medium">Bathroom</span>
                                </div>
                                <p class="font-semibold text-gray-800 text-sm">{{ property?.baths || 0 }} {{
                                    property?.baths === 1 ? 'bathroom' : 'bathrooms' }}</p>
                            </div>
                            <div>
                                <div class="flex items-center text-gray-400 mb-0.5">
                                    <ParkingCircle class="h-4 w-4 mr-1.5" />
                                    <span class="text-[11px] font-medium">Location</span>
                                </div>
                                <p class="font-semibold text-gray-800 text-sm">{{ property?.location || 'Not specified'
                                    }}</p>
                            </div>

                        </div>

                    </section>

                    <!-- Map Tab -->
                    <section v-show="activeTab === 'map'" class="bg-white p-6 rounded-lg border mb-6">

                        <div>
                            <h2 class="text-lg font-semibold text-gray-800 mb-4">Property Location</h2>
                            <div class="mb-4">
                                <p class="text-sm text-gray-600 mb-2">
                                    <MapPin class="inline-block h-4 w-4 mr-1 text-gray-500" />
                                    {{ property?.location || 'Location not available' }}
                                </p>

                            </div>

                            <!-- Map Component -->
                            <PropertyMap :latitude="48.8737" :longitude="2.3557" :zoom="15"
                                mapStyle="mapbox://styles/mapbox/streets-v12" markerColor="#8b5cf6" />

                            <div class="mt-4 text-sm text-gray-500">
                                <p class="flex items-center">
                                    <InfoIcon class="h-4 w-4 mr-1.5 text-purple-500" />
                                    The exact location will be provided after booking a viewing.
                                </p>
                            </div>
                        </div>
                    </section>
                    <section v-show="activeTab === 'visits'" class="bg-white p-6 rounded-lg shadow-md mb-6">
                        Visits tab content
                    </section>

                    <!-- Description Section -->
                    <section class="bg-white p-5 sm:p-6 rounded-lg border">
                        <h2 class="text-lg font-semibold text-gray-800 mb-3">Description</h2>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <Badge v-for="(tag, i) in tags" :key="i" variant="secondary"
                                class="bg-gray-100 text-gray-600 hover:bg-gray-200 text-xs px-2.5 py-1 font-medium">
                                {{ tag }}
                            </Badge>
                        </div>
                        <div class="text-gray-700 text-sm space-y-3 leading-relaxed">
                            <div v-if="property?.description">
                                <p>{{ property.description }}</p>
                            </div>
                            <div v-else>
                                <p>{{ property?.title }} located in {{ property?.location }}.</p>
                                <p>This {{ property?.isRental ? 'rental' : 'property' }} features {{ property?.beds }}
                                    {{ property?.beds === 1 ? 'bedroom' : 'bedrooms' }}
                                    and {{ property?.baths }} {{ property?.baths === 1 ? 'bathroom' : 'bathrooms' }}
                                    with a total area of {{ property?.area }}m².</p>
                            </div>

                            <!-- Furnished/Unfurnished information -->
                            <div class="mt-4 p-3 bg-purple-50 rounded-md border border-purple-100">
                                <p class="font-medium text-purple-800">
                                    <span v-if="showFurnishedPrice">
                                        This property is available furnished for {{ currentPrice }}.
                                        <button @click="showFurnishedPrice = false"
                                            class="text-purple-600 underline ml-1">
                                            View unfurnished price
                                        </button>
                                    </span>
                                    <span v-else>
                                        This property is available unfurnished for {{ currentPrice }}.
                                        <button @click="showFurnishedPrice = true"
                                            class="text-purple-600 underline ml-1">
                                            View furnished price
                                        </button>
                                    </span>
                                </p>
                            </div>

                            <p>Contact the agent for more details about this {{ property?.type || 'property' }}.</p>
                        </div>
                    </section>
                </div>

                <!-- Sidebar -->
                <aside class="lg:w-[380px] flex-shrink-0 mt-8 lg:mt-0 space-y-5">
                    <!-- Booking Card -->
                    <div class="bg-white p-5 rounded-lg border">
                        <div class="flex justify-between items-center mb-3.5">
                            <div>
                                <h3 class="font-semibold text-gray-800 text-base">{{ property?.agent || 'Agent Name' }}
                                </h3>
                                <p class="text-xs text-gray-500">Property Developer</p>
                            </div>
                            <Avatar class="h-9 w-9">
                                <AvatarImage
                                    :src="property?.agentImage || 'https://randomuser.me/api/portraits/women/33.jpg'"
                                    :alt="property?.agent || 'Agent'" />
                                <AvatarFallback>{{ property?.agent ? property.agent.charAt(0) : 'A' }}</AvatarFallback>
                            </Avatar>
                        </div>
                        <div
                            class="bg-orange-50 border-l-4 border-orange-400 text-orange-700 p-3 text-xs mb-4 rounded-r-sm flex items-center">
                            <AlertTriangle class="h-4 w-4 mr-2 flex-shrink-0 text-orange-500" />
                            <p class="font-semibold">This property is in high demand!</p>
                        </div>
                        <!-- <p class="text-xs font-medium text-gray-700 mb-2.5">Book your appointment online</p>
                        <div class="space-y-2">
                            <Button variant="outline" class="w-full border border-purple-600 text-purple-600" @click="openBooking">Book Your Virtual Viewing</Button>
                            <Button variant="outline" class="w-full" @click="openBooking(false)">Book Your Physical Viewing</Button>
                        </div>

                        <div class="my-4 border-t border-gray-200"></div> -->

                        <div>
                            <Button 
                                class="w-full bg-green-600 hover:bg-green-700 hover:cursor-pointer text-white font-medium flex items-center justify-center gap-2"
                                @click="contactViaWhatsApp">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="white" class="flex-shrink-0">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z"/>
                                </svg>
                                Contact via WhatsApp
                            </Button>
                            <p class="text-xs text-gray-500 mt-2 text-center">
                                Begin your journey to homeownership with a direct conversation with our agent
                            </p>
                        </div>

                    </div>

                    <!-- Property Reference Card -->
                    <div class="bg-white p-5 rounded-lg border border-gray-200  duration-300">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-semibold text-gray-800">Property Reference</h3>
                            <Badge variant="outline" class="bg-purple-50 text-purple-700 border-purple-200 text-[10px] px-2 py-0.5">
                                Important
                            </Badge>
                        </div>

                        <div class="relative">
                            <div class="flex items-center justify-between bg-gray-50 p-4 rounded-lg border border-gray-100">
                                <div class="flex items-center">
                                    <div class="bg-purple-100 rounded-full p-1.5 mr-3" @click="copyPropertyReference">
                                        <Copy class="h-4 w-4 text-purple-600" />
                                    </div>
                                    <div>
              
                                        <div class="text-xs font-medium text-gray-800 tracking-wide">{{ propertyReference }}</div>
                                    </div>
                                </div>

                         
                            </div>

                            <!-- Success overlay when copied -->
                            <div v-if="isCopied" class="absolute inset-0 bg-green-50 bg-opacity-70 rounded-lg flex items-center justify-center animate-pulse">
                                <div class="bg-white rounded-full p-2 shadow-md">
                                    <ClipboardCheck class="h-6 w-6 text-green-500" />
                                </div>
                            </div>
                        </div>

                        <p class="text-xs text-gray-600 mt-3 flex items-start">
                            <InfoIcon class="h-3.5 w-3.5 mr-1.5 text-gray-400 mt-0.5 flex-shrink-0" />
                            Please include this reference code when contacting our agents about this property.
                        </p>
                    </div>

                    <!-- Tips Card -->
                    <div class="bg-purple-50 p-4 rounded-lg border border-purple-600 flex items-center space-x-3">
                        <div class="flex-grow text-xs">
                            <div class="flex items-center text-purple-700 font-medium">
                                <Check class="h-3.5 w-3.5 mr-1.5 text-purple-600 flex-shrink-0" />
                                <span>Less than 10 visits scheduled</span>
                            </div>
                            <p class="text-gray-600 mt-0.5">Individual visit with the agent</p>
                            <p class="text-gray-600 mt-0.5">There's room for you, book today!</p>
                        </div>
                        <Lightbulb class="h-8 w-8 text-yellow-400 flex-shrink-0" />
                    </div>

                </aside>
            </div>
        </main>
    </div>
</template>
