<script lang="ts" setup>
import { PropertyCard } from '@/components/property';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, SlidersHorizontal, X } from 'lucide-vue-next';
import { usePropertiesStore } from '@/stores/properties';
import { ref, computed, watch } from 'vue';
// Set page title
useHead({
  title: 'Discover Our Properties'
});

// Get the properties store
const propertiesStore = usePropertiesStore();

// Search state
const searchQuery = ref('');
const activeFilters = ref<string[]>([]);
const isSearching = ref(false);

// Fetch all properties with useAsyncData
const { data: allProperties, error, refresh, pending } = await useAsyncData(
  'all-properties',
  () => propertiesStore.fetchProperties(),
  {
    server: true,
    lazy: false,
    default: () => []
  }
);

// Filtered properties based on search query and filters
const filteredProperties = computed(() => {
  if (!allProperties.value) return [];

  let results = [...allProperties.value];

  // Apply search query if it exists
  if (searchQuery.value.trim()) {
    results = propertiesStore.searchProperties(searchQuery.value);
  }

  // Apply active filters if any
  if (activeFilters.value.length > 0) {
    // Filter logic here - this is a simplified example
    // In a real app, you would have more complex filtering
    if (activeFilters.value.includes('For Sale')) {
      results = results.filter(p => !p.isRental);
    }
    if (activeFilters.value.includes('For Rent')) {
      results = results.filter(p => p.isRental);
    }
    // Add more filter conditions as needed
  }

  return results;
});

// Stats for showing x of y
const totalProperties = computed(() => allProperties.value?.length || 0);
const shownProperties = computed(() => filteredProperties.value.length);

// Handle search input
const handleSearch = () => {
  isSearching.value = true;
};

// Add a filter
const addFilter = (filter: string) => {
  if (!activeFilters.value.includes(filter)) {
    activeFilters.value.push(filter);
  }
};

// Remove a filter
const removeFilter = (filter: string) => {
  activeFilters.value = activeFilters.value.filter(f => f !== filter);
};

// Clear all filters
const clearAllFilters = () => {
  activeFilters.value = [];
  searchQuery.value = '';
};

// Watch for changes in search query
watch(searchQuery, (newValue) => {
  if (newValue === '') {
    isSearching.value = false;
  }
});
</script>
<template>
    <header class="w-screen h-80 overflow-hidden relative flex items-center">
        <div class="absolute inset-0 z-0 bg-cover bg-top bg-[url(/imgs/house_1.jpg)]">
            <div class="absolute inset-0 bg-black opacity-70"></div>
        </div>
        <div class="container relative z-10 px-8 md:px-25 text-white mt-10">
            <div class="max-w-2xl">
                <h1 class="text-3xl md:text-5xl font-title font-semibold mb-4 animate-fade-in">
                    Discover Our Properties
                </h1>
                <p class="animate-fade-in text-xs md:text-lg">
                    Explore our curated selection of exceptional properties, each offering unique lifestyle advantages
                    and premium features.
                </p>
            </div>
        </div>
    </header>
    <section >
        <div class="container px-6 mx-auto flex flex-col gap-5">
            <div class="flex justify-between items-center pt-8 pb-2">
                <div class="relative w-full max-w-md items-center">
                    <Input
                        id="search"
                        v-model="searchQuery"
                        type="text"
                        placeholder="Search by name, location, or type..."
                        class="pl-10 py-5 shadow-none rounded-full"
                        @keyup.enter="isSearching = true"
                    />
                    <span class="absolute start-0 inset-y-0 flex items-center justify-center px-2 pl-4">
                        <Search class="size-4 text-muted-foreground" />
                    </span>
                </div>
                <Button variant="outline" class="shadow-none" @click="activeFilters = activeFilters.length ? [] : ['For Sale', 'For Rent']">
                    <SlidersHorizontal class="w-4 h-4 mr-2" /> Filters
                </Button>
            </div>

            <!-- Active filters -->
            <div class="flex flex-wrap gap-4" v-if="activeFilters.length > 0 || searchQuery">
                <div v-if="searchQuery" class="flex-shrink p-2 px-4 rounded-full border-2 border-purple-600 bg-purple-50 text-purple-700 flex gap-4 items-center">
                    Search: {{ searchQuery }}
                    <span @click="searchQuery = ''; isSearching = false">
                        <X class="size-4 hover:size-5 transition-all duration-200 cursor-pointer"></X>
                    </span>
                </div>

                <div
                    v-for="filter in activeFilters"
                    :key="filter"
                    class="flex-shrink p-2 px-4 rounded-full border-2 border-gray-300 bg-gray-50 flex gap-4 items-center"
                >
                    {{ filter }}
                    <span @click="removeFilter(filter)">
                        <X class="size-4 hover:size-5 transition-all duration-200 cursor-pointer"></X>
                    </span>
                </div>

                <Button
                    v-if="activeFilters.length > 0 || searchQuery"
                    variant="ghost"
                    size="sm"
                    class="text-gray-500 hover:text-gray-700"
                    @click="clearAllFilters"
                >
                    Clear all
                </Button>
            </div>

            <!-- Results count -->
            <div class="flex justify-between items-center my-4">
                <p class="text-sm text-gray-500">
                    Showing <span class="font-medium text-gray-700">{{ shownProperties }}</span> of <span class="font-medium text-gray-700">{{ totalProperties }}</span> properties
                </p>

                <div class="flex items-center gap-2">
                    <span class="text-sm text-gray-500">Sort by:</span>
                    <select class="text-sm border-gray-300 rounded-md">
                        <option>Newest</option>
                        <option>Price: Low to High</option>
                        <option>Price: High to Low</option>
                    </select>
                </div>
            </div>

            <!-- Loading state -->
            <div v-if="pending" class="py-12 flex justify-center">
                <div class="animate-pulse flex flex-col items-center">
                    <div class="h-10 w-10 bg-purple-200 rounded-full mb-4"></div>
                    <p class="text-gray-500">Loading properties...</p>
                </div>
            </div>

            <!-- Error state -->
            <div v-else-if="error" class="py-12 flex justify-center">
                <div class="text-red-500 flex flex-col items-center">
                    <p class="font-medium">Failed to load properties</p>
                    <p class="text-sm">Please try again later</p>
                    <Button variant="outline" class="mt-4" @click="refresh">Retry</Button>
                </div>
            </div>

            <!-- Results -->
            <Suspense>
                <template #default>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <PropertyCard
                            v-for="property in filteredProperties"
                            :key="property.id"
                            :property="property"
                        />

                        <!-- No results message -->
                        <div v-if="filteredProperties.length === 0 && !pending && !error" class="col-span-full py-12 text-center">
                            <p class="text-gray-500 font-medium">No properties found matching your criteria</p>
                            <Button variant="link" class="mt-2" @click="clearAllFilters">Clear all filters</Button>
                        </div>
                    </div>
                </template>
                <template #fallback>
                    <div class="py-12 flex justify-center">
                        <div class="animate-pulse flex flex-col items-center">
                            <div class="h-10 w-10 bg-purple-200 rounded-full mb-4"></div>
                            <p class="text-gray-500">Loading properties...</p>
                        </div>
                    </div>
                </template>
            </Suspense>

        </div>
    </section>
    <section class="py-12 bg-base-900 sm:py-16 lg:py-20 xl:py-24">
    <div class=" mx-auto max-w-7xl ">
        <div class="relative overflow-hidden bg-gray-900 md:flex rounded-xl">
            <div class="absolute right-0 transform -translate-y-1/2 translate-x-80 md:translate-x-36 top-1/2 md:top-0 md:-translate-y-24">
                <svg class="blur-3xl filter" style="filter: blur(64px)" width="518" height="337" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M297.629 36.521C425.034 36.521 518-55.783 518 53.097S184.03 337 56.625 337c-127.406 0 0-175.023 0-283.903 0-108.88 113.599-16.576 241.004-16.576Z" fill="url(#a)" />
                    <defs>
                        <linearGradient id="a" x1="0" y1="337" x2="36.01" y2="-45.389" gradientUnits="userSpaceOnUse">
                            <stop offset="0%" style="stop-color: var(--color-cyan-500)" />
                            <stop offset="100%" style="stop-color: var(--color-purple-500)" />
                        </linearGradient>
                    </defs>
                </svg>
            </div>

            <div class="absolute inset-0">
                <img class="object-cover w-full h-full opacity-50" src="https://landingfoliocom.imgix.net/store/collection/dusk/images/noise.png" alt="" />
            </div>

            <div class="absolute inset-y-0 right-0 hidden md:block">
                <img class="w-full max-w-md transform -translate-y-20" src="/imgs/bg_place.png" alt="" />
            </div>

            <div class="relative w-full p-6 sm:p-10 lg:py-16 lg:px-20 md:w-1/2 lg:w-2/3">
                <h2 class="text-3xl font-normal text-white sm:text-4xl lg:text-5xl">Still Searching? We're Here to Help</h2>
                <p class="max-w-sm mt-6 text-base font-normal text-gray-400 sm:text-lg">Just tell us what matters to you — we'll find homes that match your needs perfectly.</p>
                <div class="relative inline-flex items-center justify-center mt-8 sm:mt-12 group">
                    <div class="absolute transition-all duration-200 rounded-full -inset-px bg-gradient-to-r from-cyan-500 to-purple-500 group-hover:shadow-lg group-hover:shadow-cyan-400/20"></div>
                    <a href="#" title="" class="relative inline-flex items-center justify-center px-8 py-3 text-base font-normal text-white bg-black border border-transparent rounded-full" role="button"> Help Me Find a Home </a>
                </div>
            </div>

            <div class="relative md:hidden">
                <img class="w-full" src="/imgs/bg_place.png" alt="" />
            </div>
        </div>
    </div>
</section>

</template>