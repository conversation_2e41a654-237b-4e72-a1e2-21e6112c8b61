<script setup>
import { NavBar } from '@/components/nav'
import { Footer } from '@/components/layout';

// Set default title template
useHead({
  titleTemplate: (titleChunk) => {
    return titleChunk ? `${titleChunk} - Dwello` : 'Dwello';
  },
  // Default meta tags
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'format-detection', content: 'telephone=no' },

    // Default Open Graph meta tags
    { property: 'og:site_name', content: 'Dwello' },
    { property: 'og:type', content: 'website' },
    { property: 'og:title', content: 'Dwello - Premium Real Estate' },
    { property: 'og:description', content: 'Discover exceptional properties curated for your lifestyle. Find your dream home with Dwello.' },
    { property: 'og:image', content: '/imgs/dwello_og.png' },
    { property: 'og:url', content: 'https://dwelloprop.com' },

    // Twitter Card meta tags
    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: 'Dwello - Premium Real Estate' },
    { name: 'twitter:description', content: 'Discover exceptional properties curated for your lifestyle. Find your dream home with Dwello.' },
    { name: 'twitter:image', content: '/imgs/dwello_og.png' }
  ],
  link: [
    { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
  ]
})
</script>

<template>
  <NuxtLoadingIndicator :height="5"></NuxtLoadingIndicator>
  <div class="min-h-screen">
    <NavBar />

    <NuxtPage />

    <Footer />

  </div>
</template>
