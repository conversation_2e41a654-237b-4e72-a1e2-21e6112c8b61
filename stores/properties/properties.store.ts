import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Property } from '@/types/property';
import { sampleListings } from './dummy/propertylists';

export const usePropertiesStore = defineStore('properties', () => {
  // State
  const properties = ref<Property[]>([]);
  const currentProperty = ref<Property | null>(null);

  // Getters
  const getPropertyById = (id: string) => {
    return properties.value.find(property => property.id === id) || null;
  };

  const featuredProperties = computed(() => {
    return properties.value.filter(property => property.featured);
  });

  /**
   * Get featured properties limited to a specific count
   * @param limit Maximum number of properties to return
   * @returns Limited array of featured properties
   */
  const limitedFeaturedProperties = (limit = 3) => {
    return properties.value
      .filter(property => property.featured)
      .slice(0, limit);
  };

  const topPickProperties = computed(() => {
    return properties.value.filter(property => property.isTopPick);
  });

  const rentalProperties = computed(() => {
    return properties.value.filter(property => property.isRental);
  });

  const propertiesForSale = computed(() => {
    return properties.value.filter(property => !property.isRental);
  });

  // Actions
  /**
   * Fetch all properties
   * In a real application, this would make an API call
   * @throws Error if the fetch operation fails
   */
  async function fetchProperties() {
    try {
      // Simulate API call with a timeout
      await new Promise(resolve => setTimeout(resolve, 500));

      // For now, we'll use the sample data
      properties.value = sampleListings;

      return properties.value;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch properties';
      console.error('Error fetching properties:', err);
      throw new Error(errorMessage);
    }
  }

  /**
   * Fetch a property by ID
   * @param id The property ID to fetch
   * @throws Error if the property is not found or the fetch operation fails
   */
  async function fetchPropertyById(id: string) {
    try {
      // Simulate API call with a timeout
      await new Promise(resolve => setTimeout(resolve, 300));

      // For now, we'll find the property in our sample data
      const property = sampleListings.find(p => p.id === id);

      if (!property) {
        throw new Error(`Property with ID ${id} not found`);
      }

      currentProperty.value = property;
      return property;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to fetch property with ID ${id}`;
      console.error(`Error fetching property with ID ${id}:`, err);
      throw new Error(errorMessage);
    }
  }

  /**
   * Clear the current property
   */
  function clearCurrentProperty() {
    currentProperty.value = null;
  }

  /**
   * Search properties by query
   * @param query The search query
   */
  function searchProperties(query: string) {
    if (!query) return properties.value;

    const lowercaseQuery = query.toLowerCase();

    return properties.value.filter(property =>
      property.title.toLowerCase().includes(lowercaseQuery) ||
      property.location.toLowerCase().includes(lowercaseQuery) ||
      property.type.toLowerCase().includes(lowercaseQuery)
    );
  }

  /**
   * Filter properties by criteria
   * @param criteria The filter criteria
   */
//   function filterProperties(criteria: {
//     minPrice?: number;
//     maxPrice?: number;
//     beds?: number;
//     baths?: number;
//     minArea?: number;
//     maxArea?: number;
//     type?: string;
//     isRental?: boolean;
//   }) {
//     return properties.value.filter(property => {
//       // Convert price string to number (remove $ and commas)
//       const numericPrice = Number(property.price.replace(/[$,]/g, ''));

//       if (criteria.minPrice && numericPrice < criteria.minPrice) return false;
//       if (criteria.maxPrice && numericPrice > criteria.maxPrice) return false;
//       if (criteria.beds && property.beds < criteria.beds) return false;
//       if (criteria.baths && property.baths < criteria.baths) return false;
//       if (criteria.minArea && property.area < criteria.minArea) return false;
//       if (criteria.maxArea && property.area > criteria.maxArea) return false;
//       if (criteria.type && property.type !== criteria.type) return false;
//       if (criteria.isRental !== undefined && property.isRental !== criteria.isRental) return false;

//       return true;
//     });
//   }

  // Return everything that should be exposed
  return {
    // State
    properties,
    currentProperty,

    // Getters
    getPropertyById,
    featuredProperties,
    limitedFeaturedProperties,
    topPickProperties,
    rentalProperties,
    propertiesForSale,

    // Actions
    fetchProperties,
    fetchPropertyById,
    clearCurrentProperty,
    searchProperties,
    // filterProperties
  };
});